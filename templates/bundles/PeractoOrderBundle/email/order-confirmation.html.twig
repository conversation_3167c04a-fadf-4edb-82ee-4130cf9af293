{% import "@PeractoCore/email/constants/styles.twig" as styles %}

{% block body_html %}
    {{ include('@PeractoCore/email/layout/_head.html.twig') }}
    {{ include('@PeractoCore/email/layout/_header.html.twig') }}
    {% set hasDiscount = (order.totalDiscountExcTax is not null and order.totalDiscountExcTax > 0) %}
    {% set appliedPromotions = order.promotionNames %}

    <!-- Email Header : BEGIN -->
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; font-size: 12px;">
        <tr>
            <td colspan="2" style="padding: 0px;">
                <table style="width: 100%;">
                    <tr>
                        <td style="padding: 30px;">
                            <h1 style="font-family: {{ styles.headingFontFamily() }}; text-transform:uppercase; margin-bottom: 0px; font-size: 30px;text-align:center;font-weight:{{ styles.headingFontWeight() }};">
                                {{ subject }}
                            </h1>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; line-height: 160%;">
        <tr>
            <td style="padding: 20px 20px 0px 20px; width: 100%; font-family: {{ styles.fontFamily() }}; line-height: 140%; text-align: left; color: {{ styles.textColour() }};" class="x-gmail-data-detectors">
                <p style="margin-bottom: 20px">{{ 'peracto.order_bundle.email.order_confirmation.welcome_message'|trans({'%first_name%': order.billingAddress.forename, '%last_name%': order.billingAddress.surname }) }}</p>
            </td>
        </tr>
    </table>

    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; line-height: 160%;">
        <tr>
            <td style="width: 100%; font-family: {{ styles.fontFamily() }}; line-height: 140%; text-align: left; color: {{ styles.textColour() }}; " class="x-gmail-data-detectors">
                <table width="100%">
                    <tbody>
                    <tr>
                        <td style="width: 50%; vertical-align: top; padding: 20px 20px;">
                            <table width="100%">
                                <tbody>
                                <tr>
                                    <td style="vertical-align: top;">

                                        <p style="font-size: 14px; margin-bottom: 0px;"><strong>{{ 'peracto.order_bundle.email.order_confirmation.bill_to'|trans }}: </strong>{{ order.billingAddress.forename }} {{ order.billingAddress.surname }}</p>
                                        <p style="font-size: 14px; margin-bottom: 0px;">{{ order.billingAddress.line1 }}</p>

                                        {% if order.billingAddress.line2 is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.line2 }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.line3 is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.line3 }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.line4 is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.line4 }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.city is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.city }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.county is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.county }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.postalCode is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.postalCode }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.country is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px;">
                                                {{ order.billingAddress.country }}
                                            </p>
                                        {% endif %}

                                        {% if order.billingAddress.telephone is not empty %}
                                            <p style="font-size: 14px; margin-bottom: 0px; margin-top: 10px;">{{ order.billingAddress.telephone }}</p>
                                        {% endif %}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                        <td style="width: 50%; vertical-align: top; padding: 20px 20px;">
                            <table width="100%">
                                <tbody>
                                <tr>
                                    <td style="vertical-align: top;">

                                        <p style="font-size: 14px; margin-bottom: 0px;"><strong>{{ 'peracto.order_bundle.email.order_confirmation.deliver_to'|trans }}: </strong>{{ order.deliveryAddress.forename|trans }} {{ order.deliveryAddress.surname }}</p>

                                        {% if order.shippingServiceLocationName is defined and order.shippingServiceLocationName|length > 0 %}
                                            {# Click and Collect #}
                                            <p style="font-size: 14px; margin-bottom: 0px;"><strong>{{ 'order.click_and_collect.click_and_collect'|trans }}:</strong></p>
                                            {% if order.collectInOneHour %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">{{ 'order.click_and_collect.collect_in_1_hour'|trans({'%location%': order.shippingServiceLocationName}) }}</p>
                                            {% else %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">{{ 'order.click_and_collect.collect_in_48_hours'|trans({'%location%': order.shippingServiceLocationName}) }}</p>
                                            {% endif %}

                                        {% else %}
                                            {# Delivery #}
                                            <p style="font-size: 14px; margin-bottom: 0px;">{{ order.deliveryAddress.line1 }}</p>

                                            {% if order.deliveryAddress.line2 is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.line2 }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.line3 is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.line3 }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.line4 is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.line4 }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.city is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.city }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.county is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.county }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.postalCode is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.postalCode }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.country is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px;">
                                                    {{ order.deliveryAddress.country }}
                                                </p>
                                            {% endif %}

                                            {% if order.deliveryAddress.telephone is not empty %}
                                                <p style="font-size: 14px; margin-bottom: 0px; margin-top: 10px;">{{ order.deliveryAddress.telephone }}</p>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>

    {% if order.deliveryNotes is not empty %}
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; line-height: 160%;">
            <tr>
                <td style="padding: 20px 20px 0px 20px; width: 100%; font-family: {{ styles.fontFamily() }}; line-height: 140%; text-align: left; color: {{ styles.textColour() }};" class="x-gmail-data-detectors">
                    <p style="margin-bottom: 20px"><span style="font-weight: bold;">Delivery Note:</span> {{ order.deliveryNotes }}</p>
                </td>
            </tr>
        </table>
    {% endif %}

     {% if order.purchaseOrder is not empty %}
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; line-height: 160%;">
            <tr>
                <td style="padding: 20px 20px 0px 20px; width: 100%; font-family: {{ styles.fontFamily() }}; line-height: 140%; text-align: left; color: {{ styles.textColour() }};" class="x-gmail-data-detectors">
                    <p style="margin-bottom: 20px"><span style="font-weight: bold;">Purchase Order:</span> {{ order.purchaseOrder }}</p>
                </td>
            </tr>
        </table>
    {% endif %}

    <table role="presentation" cellspacing="0" cellpadding="0" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; font-size: 12px; line-height: 140%;">
        <tr>
            <td style="width: 100%; font-family: {{ styles.fontFamily() }}; font-size: 12px; line-height: 140%; text-align: left; color: {{ styles.textColour() }};" class="x-gmail-data-detectors">
                <table width="100%" cellpadding="10">
                    <tbody>
                    {% if order.getLines|length %}
                        {% for row in order.getLines %}
                            <tr>
                                <td style="width: 140px;padding:10px;">
                                    {% set src = row.item.image and row.item.image|length ? row.item.image : asset('placeholder.jpg') %}
                                    <img src="{{ src }}" width="140"/>
                                </td>
                                <td style="padding:10px;">
                                    <table role="presentation" cellspacing="0" cellpadding="5" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; line-height: 160%;">
                                        <tr>
                                            <td colspan="5">
                                                <h5 style="font-family: {{ styles.headingFontFamily() }}; text-transform:uppercase; margin-bottom:10px;font-weight:{{ styles.headingFontWeight() }};">{{ row.item.name }}</h5>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="20%" style="text-align:left;">
                                                {{ row.sku }}
                                            </td>
                                            <td width="20%">
                                                {{ row.unitPrice.excTax|convert_to_decimal|format_currency(order.currency) }}
                                            </td>
                                            <td width="10%">{{ 'peracto.order_bundle.email.order_confirmation.line.qty'|trans }}: {{ row.quantity }}</td>
                                            <td width="30%">{{ 'peracto.order_bundle.email.order_confirmation.line.lead_time_number_of_days'|trans({days: row.leadTime}) }}</td>
                                            <td width="20%" align="right" style="color: {{ styles.linkColour() }};">
                                                {% if hasDiscount %}
                                                    <span style="text-decoration: line-through !important;">({{ row.linePrice.excTax|convert_to_decimal|format_currency(order.currency) }})</span>
                                                {% endif %}
                                                <strong>{{ row.linePriceTotal.excTax|convert_to_decimal|format_currency(order.currency) }}</strong>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                    </tbody>
                </table>
            </td>
        </tr>
    </table>

    <table role="presentation" cellspacing="0" cellpadding="5" border="0" align="center" width="100%" style="max-width: 680px; font-family: {{ styles.fontFamily() }}; color: {{ styles.textColour() }}; font-size: 12px; line-height: 140%;">
        <tr>
            <td style="padding: 10px 0; width: 100%; font-family: {{ styles.fontFamily() }}; font-size: 12px; line-height: 140%; text-align: right; color: {{ styles.textColour() }};" class="x-gmail-data-detectors">
                <table width="100%" cellpadding="10">
                    <tr>
                        <td style="width: 140px"></td>
                        <td width="25%"></td>
                        <td width="25%" style="font-family: {{ styles.fontFamily() }}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour() }};">{{ order.shippingServiceName }}</td>
                        <td width="25%" align="right" style="font-family: {{ styles.fontFamily() }}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};" ><strong>{{ order.shippingTotalAfterDiscountExcTax|convert_to_decimal|format_currency(order.currency) }}</strong></td>
                    </tr>

                    {% for promotion in appliedPromotions %}
                        <tr>
                            <td style="width: 140px"></td>
                            <td width="25%"></td>
                            <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}}; vertical-align: top;">
                                {% if loop.first %}
                                    {{ 'peracto.order_bundle.email.order_confirmation.promotions'|trans }}
                                {% endif %}
                            </td>
                            <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}}; vertical-align: top;" ><strong>{{ promotion }}</strong></td>
                        </tr>
                    {% endfor %}

                    {% if hasDiscount %}
                        <tr>
                            <td style="width: 140px"></td>
                            <td width="25%"></td>
                            <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};">{{ 'peracto.order_bundle.email.order_confirmation.totals.total_discount'|trans }}</td>
                            <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};" ><strong>- {{ order.totalDiscountExcTax|convert_to_decimal|format_currency(order.currency) }}</strong></td>
                        </tr>
                    {% endif %}

                    <tr>
                        <td style="width: 140px"></td>
                        <td width="25%"></td>
                        <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};">{{ 'peracto.order_bundle.email.order_confirmation.totals.tax'|trans }}</td>
                        <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};" ><strong>{{ order.totalTaxAfterDiscount|convert_to_decimal|format_currency(order.currency) }}</strong></td>
                    </tr>

                    <tr>
                        <td style="width: 140px"></td>
                        <td width="25%"></td>
                        <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};" ><strong>{{ 'peracto.order_bundle.email.order_confirmation.totals.order_total'|trans }}</strong></td>
                        <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 16px; line-height: 140%; text-align: right; color: {{ styles.headerBorderColour()}};" ><strong>{{ order.totalIncTaxAfterDiscount|convert_to_decimal|format_currency(order.currency) }}</strong></td>
                    </tr>

                    {% if order.paymentTransactions|length > 0 %}
                        {% set paymentTransaction = order.paymentTransactions|first %}
                        <tr>
                            <td style="width: 140px"></td>
                            <td width="25%"></td>
                            <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}}; padding-top: 20px;" ><strong>Payment Method</strong></td>
                            <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}}; padding-top: 20px;"></td>
                        </tr>
                        <tr>
                            <td style="width: 140px"></td>
                            <td width="25%"></td>
                            <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};">Type:</td>
                            <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};">
                                {% if paymentTransaction.provider == 'braintree' %}
                                    CREDIT CARD
                                {% elseif paymentTransaction.provider == 'payOnAccount' %}
                                    PAY ON ACCOUNT
                                {% else %}
                                    {{ paymentTransaction.provider|upper }}
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 140px"></td>
                            <td width="25%"></td>
                            <td width="25%" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};">Amount:</td>
                            <td width="25%" align="right" style="font-family: {{ styles.fontFamily()}}; font-size: 14px; line-height: 140%; text-align: right; color: {{ styles.textColour()}};">
                                {% if paymentTransaction.currency == 'GBP' %}
                                    £{{ paymentTransaction.transactionAmount|convert_to_decimal|number_format(2, '.', ',') }}
                                {% elseif paymentTransaction.currency == 'EUR' %}
                                    €{{ paymentTransaction.transactionAmount|convert_to_decimal|number_format(2, '.', ',') }}
                                {% else %}
                                    {{ paymentTransaction.transactionAmount|convert_to_decimal|format_currency(paymentTransaction.currency) }}
                                {% endif %}
                            </td>
                        </tr>
                    {% endif %}

                </table>
            </td>
        </tr>
    </table>

    {{ include('@PeractoCore/email/layout/_footer.html.twig') }}
{% endblock %}
