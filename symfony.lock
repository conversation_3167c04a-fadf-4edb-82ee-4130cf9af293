{"api-platform/core": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "adf0c75f4bed8b0043a6680376323404953578c5"}, "files": ["config/packages/api_platform.yaml", "config/routes/api_platform.yaml", "src/ApiResource/.gitignore"]}, "aws/aws-sdk-php-symfony": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "d1753f9e2a669c464b2b0618af9b0123426b67b4"}, "files": ["config/packages/aws.yaml"]}, "ckfinder/ckfinder-symfony-bundle": {"version": "4.0.1"}, "dama/doctrine-test-bundle": {"version": "6.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "2c920f73a217f30bd4a37833c91071f4d3dc1ecd"}, "files": ["config/packages/test/dama_doctrine_test_bundle.yaml"]}, "doctrine/annotations": {"version": "1.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "91690c0a440faba1a3676256bcca2b4aa9f55b72"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "gesdinet/jwt-refresh-token-bundle": {"version": "1.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "13fb2b58ec6204211815eb6f4c69e4918e5af617"}, "files": ["config/packages/gesdinet_jwt_refresh_token.yaml", "config/routes/gesdinet_jwt_refresh_token.yaml", "src/Entity/RefreshToken.php"]}, "hautelook/alice-bundle": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.2", "ref": "c84e4f2b9d7f436d7d52e8369230b393367607ec"}, "files": ["config/packages/hautelook_alice.yaml", "fixtures/.gitignore"]}, "lexik/jwt-authentication-bundle": {"version": "2.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "nelmio/alice": {"version": "3.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "42b52d2065dc3fde27912d502c18ca1926e35ae2"}, "files": ["config/packages/nelmio_alice.yaml"]}, "nelmio/cors-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "peracto/background-bundle": {"version": "63.6.0"}, "peracto/catalogue-bundle": {"version": "63.6.0"}, "peracto/configuration-bundle": {"version": "63.6.0"}, "peracto/content-bundle": {"version": "63.6.0"}, "peracto/core-bundle": {"version": "63.6.0"}, "peracto/form-bundle": {"version": "63.6.0"}, "peracto/invoice-bundle": {"version": "63.6.0"}, "peracto/location-bundle": {"version": "63.6.0"}, "peracto/menu-bundle": {"version": "63.6.0"}, "peracto/order-bundle": {"version": "63.6.0"}, "peracto/redirects-bundle": {"version": "63.6.0"}, "peracto/shipping-bundle": {"version": "63.6.0"}, "peracto/task-bundle": {"version": "63.6.0"}, "peracto/user-bundle": {"version": "63.6.0"}, "peracto/wishlist-bundle": {"version": "63.6.0"}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "ramsey/uuid-doctrine": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "471aed0fbf5620b8d7f92b7a5ebbbf6c0945c27a"}, "files": ["config/packages/ramsey_uuid_doctrine.yaml"]}, "symfony/amazon-mailer": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.4", "ref": "9648db3ecae5c8a6b1a5f74715d3907124348815"}}, "symfony/console": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "af47254c5e4cd543e6af3e4508298ffebbdaddd3"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/mailer": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "df66ee1f226c46f01e85c29c2f7acce0596ba35a"}, "files": ["config/packages/mailer.yaml"]}, "symfony/messenger": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "8a5b112826f7d3d5b07027f93786ae11a1c7de48"}, "files": ["config/packages/security.yaml"]}, "symfony/translation": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "b7772eb20e92f3fb4d4fe756e7505b4ba2ca1a2c"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "theofidry/alice-data-fixtures": {"version": "1.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fe5a50faf580eb58f08ada2abe8afbd2d4941e05"}}}